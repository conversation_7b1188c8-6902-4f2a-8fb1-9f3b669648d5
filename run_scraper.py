#!/usr/bin/env python3
"""
Simple script to run the Zangia scraper with default settings
"""

from zangia_scraper import ZangiaScraper
import time

def main():
    print("Starting Zangia.mn Company Information Scraper")
    print("=" * 50)
    
    # Initialize scraper with default settings
    scraper = ZangiaScraper(
        max_workers=5,      # Number of concurrent threads
        timeout=20,         # Request timeout in seconds
        max_retries=3       # Maximum retries per URL
    )
    
    # Load URLs from file
    urls = scraper.load_urls_from_file('asd.txt')
    if not urls:
        print("Error: Could not load URLs from asd.txt")
        return
    
    print(f"Found {len(urls)} company URLs to process")
    
    # Ask user if they want to process all or just a sample
    while True:
        choice = input("\nProcess all companies? (y/n) or enter number for sample: ").strip().lower()
        if choice in ['y', 'yes']:
            break
        elif choice in ['n', 'no']:
            print("Exiting...")
            return
        elif choice.isdigit():
            sample_size = int(choice)
            if sample_size > 0 and sample_size <= len(urls):
                urls = urls[:sample_size]
                print(f"Processing sample of {len(urls)} companies")
                break
            else:
                print(f"Please enter a number between 1 and {len(urls)}")
        else:
            print("Please enter 'y', 'n', or a number")
    
    # Start scraping
    start_time = time.time()
    print(f"\nStarting to scrape {len(urls)} companies...")
    
    companies_info = scraper.scrape_all_companies(urls)
    
    # Export to Excel
    output_file = scraper.export_to_excel(companies_info)
    
    # Show results
    end_time = time.time()
    scraper.print_summary(companies_info)
    print(f"\nTotal processing time: {end_time - start_time:.2f} seconds")
    print(f"Results saved to: {output_file}")
    
    # Open the file location
    import os
    print(f"\nFile saved in: {os.path.abspath(output_file)}")

if __name__ == "__main__":
    main()
