import requests
from bs4 import BeautifulSoup
import threading
import time

with open("C:\\Users\\<USER>\\Desktop\\Script\\orgio-\\asd.txt", "r", encoding="utf-8") as f:
    urls = [line.strip() for line in f.readlines() if line.strip()]

company_data = ["" for _ in urls]

MAX_RETRIES = 3
TIMEOUT = 20
THREAD_LIMIT = 5

def fetch_info(index, url, session):
    for attempt in range(MAX_RETRIES):
        try:
            response = session.get(url, timeout=TIMEOUT)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, "html.parser")
                facebook_tag = soup.select_one("div.org-fb a")
                facebook_url = facebook_tag["href"].strip() if facebook_tag else ""
                if facebook_url.startswith("fb://"):
                    facebook_url = ""
                company_data[index] = facebook_url
                print(f"done")
                return
            else:
                print(f"aldaaa ({attempt + 1}/{MAX_RETRIES}): {url} - {response.status_code}")
        except requests.RequestException as e:
            print(f"h aldaaaaa ({attempt + 1}/{MAX_RETRIES}): {url} - {e}")
        time.sleep(2)

with requests.Session() as session:
    threads = []
    for i, url in enumerate(urls):
        while threading.active_count() > THREAD_LIMIT:
            time.sleep(1)
        thread = threading.Thread(target=fetch_info, args=(i, url, session))
        threads.append(thread)
        thread.start()

    for thread in threads:
        thread.join()

with open("C:\\Users\\<USER>\\Desktop\\Script\\orgio-\\fb.txt", "w", encoding="utf-8") as file:
    for data in company_data:
        file.write(data + "\n")
