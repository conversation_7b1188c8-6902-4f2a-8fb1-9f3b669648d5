import subprocess

scripts = [
    r"C:\Users\\<USER>\\Desktop\\Script\orgio-\fb.py",
    r"C:\Users\\<USER>\\Desktop\\Script\orgio-\hayg.py",
    r"C:\Users\\<USER>\\Desktop\\Script\orgio-\joblist.py",
    r"C:\Users\\<USER>\\Desktop\\Script\orgio-\\web.py",
    r"C:\Users\\<USER>\\Desktop\\Script\orgio-\phone.py"
]

for script in scripts:
    print(f"Running: {script}")
    result = subprocess.run(["python", script])
    if result.returncode != 0:
        print(f"⚠️ Алдаа гарлаа: {script}")
