import requests
from bs4 import BeautifulSoup
import pandas as pd
import re
import threading
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin, urlparse
import os
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zangia_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class CompanyInfo:
    """Data class to store company information"""
    company_name: str = ""
    website_url: str = ""
    phone_number: str = ""
    address: str = ""
    facebook_url: str = ""
    job_listing_url: str = ""
    source_url: str = ""
    scrape_status: str = "Success"
    error_message: str = ""

class ZangiaScraper:
    """
    Comprehensive scraper for extracting company information from Zangia.mn
    """

    def __init__(self, max_workers: int = 5, timeout: int = 20, max_retries: int = 3):
        self.max_workers = max_workers
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

    def load_urls_from_file(self, file_path: str) -> List[str]:
        """Load URLs from text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                urls = [line.strip() for line in file.readlines() if line.strip()]
            logger.info(f"Loaded {len(urls)} URLs from {file_path}")
            return urls
        except FileNotFoundError:
            logger.error(f"File not found: {file_path}")
            return []
        except Exception as e:
            logger.error(f"Error loading URLs from file: {e}")
            return []

    def extract_phone_number(self, text: str) -> str:
        """Extract phone number from text using regex"""
        # Mongolian phone number patterns
        phone_patterns = [
            r'(?:\+976|976)?[-\s]?(?:\d{2}[-\s]?\d{6}|\d{8})',  # Standard Mongolian format
            r'(?:\+976|976)?[-\s]?\d{8}',  # 8 digit format
            r'\b\d{8}\b',  # Simple 8 digit
            r'\b\d{2}[-\s]\d{6}\b'  # XX-XXXXXX format
        ]

        for pattern in phone_patterns:
            matches = re.findall(pattern, text)
            if matches:
                # Clean and format the phone numbers
                cleaned_numbers = []
                for match in matches:
                    # Remove spaces and dashes
                    cleaned = re.sub(r'[-\s]', '', match)
                    # Remove country code if present
                    if cleaned.startswith('976'):
                        cleaned = cleaned[3:]
                    elif cleaned.startswith('+976'):
                        cleaned = cleaned[4:]

                    # Validate length (should be 8 digits for Mongolian numbers)
                    if len(cleaned) == 8 and cleaned.isdigit():
                        cleaned_numbers.append(cleaned)

                if cleaned_numbers:
                    return ', '.join(cleaned_numbers)

        return ""

    def scrape_job_listing_url(self, company_url: str) -> str:
        """Extract job listing URL from company page"""
        try:
            response = self.session.get(company_url, timeout=self.timeout)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Look for job list section
                job_list = soup.find(class_="job-list")
                if job_list:
                    first_job = job_list.find(class_="job item")
                    if first_job:
                        job_link = first_job.find("a")
                        if job_link and job_link.get("href"):
                            return job_link["href"]

        except Exception as e:
            logger.debug(f"Error extracting job listing URL from {company_url}: {e}")

        return ""

    def scrape_phone_from_job_page(self, job_url: str) -> str:
        """Scrape phone number from job listing page"""
        if not job_url or not job_url.startswith(('http://', 'https://')):
            return ""

        try:
            response = self.session.get(job_url, timeout=self.timeout)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # Look for contact sections
                contact_sections = soup.find_all(['div', 'section'],
                                               class_=re.compile(r'(contact|info|footer|section)', re.I))

                for section in contact_sections:
                    if "Холбоо барих" in section.text:
                        phone = self.extract_phone_number(section.text)
                        if phone:
                            return phone

                # If no specific contact section, search entire page
                phone = self.extract_phone_number(soup.get_text())
                return phone

        except Exception as e:
            logger.debug(f"Error scraping phone from job page {job_url}: {e}")

        return ""

    def scrape_company_info(self, company_url: str) -> CompanyInfo:
        """Scrape comprehensive company information from a single URL"""
        company_info = CompanyInfo(source_url=company_url)

        for attempt in range(self.max_retries):
            try:
                response = self.session.get(company_url, timeout=self.timeout)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')

                    # Extract company name
                    name_tag = soup.select_one("div.name p")
                    if name_tag:
                        company_info.company_name = name_tag.text.strip()

                    # Extract website URL
                    website_tag = soup.select_one("div.org-web a")
                    if website_tag and website_tag.get("href"):
                        website_url = website_tag["href"].strip()
                        if website_url and not website_url.startswith("javascript:"):
                            company_info.website_url = website_url

                    # Extract address
                    address_tag = soup.select_one("div.org-address")
                    if address_tag:
                        company_info.address = address_tag.text.strip()

                    # Extract Facebook URL
                    facebook_tag = soup.select_one("div.org-fb a")
                    if facebook_tag and facebook_tag.get("href"):
                        facebook_url = facebook_tag["href"].strip()
                        # Filter out mobile app links
                        if facebook_url and not facebook_url.startswith("fb://"):
                            company_info.facebook_url = facebook_url

                    # Extract job listing URL
                    job_url = self.scrape_job_listing_url(company_url)
                    company_info.job_listing_url = job_url

                    # Extract phone number from job page if available
                    if job_url:
                        phone = self.scrape_phone_from_job_page(job_url)
                        company_info.phone_number = phone

                    # If no phone found in job page, try to find it on company page
                    if not company_info.phone_number:
                        phone = self.extract_phone_number(soup.get_text())
                        company_info.phone_number = phone

                    logger.debug(f"Successfully scraped: {company_info.company_name}")
                    return company_info

                else:
                    logger.warning(f"HTTP {response.status_code} for {company_url} (attempt {attempt + 1})")

            except requests.RequestException as e:
                logger.warning(f"Request error for {company_url} (attempt {attempt + 1}): {e}")
            except Exception as e:
                logger.error(f"Unexpected error for {company_url} (attempt {attempt + 1}): {e}")

            if attempt < self.max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff

        # If all attempts failed
        company_info.scrape_status = "Failed"
        company_info.error_message = f"Failed after {self.max_retries} attempts"
        logger.error(f"Failed to scrape {company_url} after {self.max_retries} attempts")
        return company_info

    def scrape_all_companies(self, urls: List[str]) -> List[CompanyInfo]:
        """Scrape information for all companies using multithreading"""
        companies_info = []

        logger.info(f"Starting to scrape {len(urls)} companies with {self.max_workers} workers")

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_url = {executor.submit(self.scrape_company_info, url): url for url in urls}

            # Process completed tasks
            for i, future in enumerate(as_completed(future_to_url), 1):
                url = future_to_url[future]
                try:
                    company_info = future.result()
                    companies_info.append(company_info)

                    # Progress logging
                    if i % 10 == 0 or i == len(urls):
                        logger.info(f"Progress: {i}/{len(urls)} companies processed")

                except Exception as e:
                    logger.error(f"Error processing {url}: {e}")
                    # Add failed entry
                    failed_info = CompanyInfo(
                        source_url=url,
                        scrape_status="Failed",
                        error_message=str(e)
                    )
                    companies_info.append(failed_info)

        logger.info(f"Completed scraping {len(companies_info)} companies")
        return companies_info

    def export_to_excel(self, companies_info: List[CompanyInfo], filename: str = None) -> str:
        """Export company information to Excel file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"zangia_companies_{timestamp}.xlsx"

        # Convert to DataFrame
        data = []
        for company in companies_info:
            data.append({
                'Company Name': company.company_name,
                'Website URL': company.website_url if company.website_url else "No website",
                'Phone Number': company.phone_number if company.phone_number else "No phone",
                'Address': company.address,
                'Facebook URL': company.facebook_url,
                'Job Listing URL': company.job_listing_url,
                'Source URL': company.source_url,
                'Scrape Status': company.scrape_status,
                'Error Message': company.error_message
            })

        df = pd.DataFrame(data)

        # Create Excel writer with formatting
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Company Information', index=False)

            # Get the workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['Company Information']

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                worksheet.column_dimensions[column_letter].width = adjusted_width

        logger.info(f"Data exported to {filename}")
        return filename

    def print_summary(self, companies_info: List[CompanyInfo]):
        """Print summary statistics"""
        total = len(companies_info)
        successful = sum(1 for c in companies_info if c.scrape_status == "Success")
        failed = total - successful

        with_website = sum(1 for c in companies_info if c.website_url)
        with_phone = sum(1 for c in companies_info if c.phone_number)
        with_address = sum(1 for c in companies_info if c.address)
        with_facebook = sum(1 for c in companies_info if c.facebook_url)

        print("\n" + "="*50)
        print("SCRAPING SUMMARY")
        print("="*50)
        print(f"Total companies processed: {total}")
        print(f"Successfully scraped: {successful}")
        print(f"Failed: {failed}")
        print(f"Success rate: {(successful/total)*100:.1f}%")
        print("\nData completeness:")
        print(f"Companies with website: {with_website} ({(with_website/total)*100:.1f}%)")
        print(f"Companies with phone: {with_phone} ({(with_phone/total)*100:.1f}%)")
        print(f"Companies with address: {with_address} ({(with_address/total)*100:.1f}%)")
        print(f"Companies with Facebook: {with_facebook} ({(with_facebook/total)*100:.1f}%)")
        print("="*50)


def main():
    """Main function to run the scraper"""
    import argparse

    parser = argparse.ArgumentParser(description='Scrape company information from Zangia.mn')
    parser.add_argument('--input', '-i', default='asd.txt',
                       help='Input file containing company URLs (default: asd.txt)')
    parser.add_argument('--output', '-o',
                       help='Output Excel filename (default: auto-generated with timestamp)')
    parser.add_argument('--workers', '-w', type=int, default=5,
                       help='Number of worker threads (default: 5)')
    parser.add_argument('--timeout', '-t', type=int, default=20,
                       help='Request timeout in seconds (default: 20)')
    parser.add_argument('--retries', '-r', type=int, default=3,
                       help='Maximum number of retries per URL (default: 3)')
    parser.add_argument('--sample', '-s', type=int,
                       help='Process only first N URLs for testing')

    args = parser.parse_args()

    # Initialize scraper
    scraper = ZangiaScraper(
        max_workers=args.workers,
        timeout=args.timeout,
        max_retries=args.retries
    )

    # Load URLs
    urls = scraper.load_urls_from_file(args.input)
    if not urls:
        logger.error("No URLs found in input file")
        return

    # Use sample if specified
    if args.sample:
        urls = urls[:args.sample]
        logger.info(f"Processing sample of {len(urls)} URLs")

    # Scrape companies
    start_time = time.time()
    companies_info = scraper.scrape_all_companies(urls)
    end_time = time.time()

    # Export to Excel
    output_file = scraper.export_to_excel(companies_info, args.output)

    # Print summary
    scraper.print_summary(companies_info)
    print(f"\nTotal processing time: {end_time - start_time:.2f} seconds")
    print(f"Output saved to: {output_file}")


if __name__ == "__main__":
    main()