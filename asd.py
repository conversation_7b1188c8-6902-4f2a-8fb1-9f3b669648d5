import requests
from bs4 import BeautifulSoup


with open("C:\\Users\\<USER>\\Desktop\\Script\\orgio-\\asd.txt", "w", encoding="utf-8") as file:
    for x in range(1, 118):# 1ees 117 hurtelh huudas
        url = f"https://www.zangia.mn/company/pg.{x}"
        response = requests.get(url)

        if response.status_code == 200:
            soup = BeautifulSoup(response.text, "html.parser")
            
            companies_div = soup.find("div", class_="companies fabl")
            if companies_div:
                links = companies_div.find_all("a", href=True)
                
                for link_tag in links:
                    link = "https://www.zangia.mn" + link_tag["href"]
                    file.write(link + "\n")
            else:
                print(f"div aldaa {x}.")
        else:
            print(f"orohgui bn {x}.")

print("done")