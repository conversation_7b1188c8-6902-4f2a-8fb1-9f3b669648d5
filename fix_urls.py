#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the URLs in asd.txt by replacing 'zangia.mncompany' with 'zangia.mn/company'
"""

def fix_urls():
    input_file = 'asd.txt'
    output_file = 'asd_fixed.txt'
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            urls = f.readlines()
        
        fixed_urls = []
        for url in urls:
            url = url.strip()
            if url:
                # Fix the URL format
                fixed_url = url.replace('zangia.mncompany', 'zangia.mn/company')
                fixed_urls.append(fixed_url)
        
        # Write fixed URLs to new file
        with open(output_file, 'w', encoding='utf-8') as f:
            for url in fixed_urls:
                f.write(url + '\n')
        
        print(f"Fixed {len(fixed_urls)} URLs")
        print(f"Original file: {input_file}")
        print(f"Fixed file: {output_file}")
        print("\nFirst 5 fixed URLs:")
        for i, url in enumerate(fixed_urls[:5]):
            print(f"{i+1}. {url}")
        
        # Ask if user wants to replace the original file
        choice = input(f"\nReplace original file {input_file} with fixed version? (y/n): ").strip().lower()
        if choice in ['y', 'yes']:
            import shutil
            shutil.move(output_file, input_file)
            print(f"Replaced {input_file} with fixed URLs")
        else:
            print(f"Fixed URLs saved to {output_file}")
            
    except FileNotFoundError:
        print(f"Error: File {input_file} not found")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    fix_urls()
