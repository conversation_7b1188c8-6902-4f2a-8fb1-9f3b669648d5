import re
import requests
from bs4 import BeautifulSoup

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}

def scrape_phone_number(link):
    try:
        if link and link.startswith(('http://', 'https://')):
            response = requests.get(link, headers=headers, timeout=10)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                contact_sections = soup.find_all(['div', 'section'], class_=re.compile(r'(contact|info|footer|section)', re.I))
                for section in contact_sections:
                    if "Холбоо барих" in section.text:
                        phone_match = re.findall(r'(?:(?:\+976|976)?[-\s]?)?(?:\d{2}[-\s]?\d{6}|\d{8})', section.text)
                        if phone_match:
                            return ', '.join(phone_match)
    except requests.exceptions.RequestException as e:
        print(f"Алдаа: {link} - {e}")
    return ""  # hooson mur dutsaan


with open('C:\\Users\\<USER>\\Desktop\\Script\\orgio-\\joblist.txt', 'r', encoding='utf-8') as file:
    job_links = [line.rstrip('\n') for line in file]

with open("C:\\Users\\<USER>\\Desktop\\Script\\orgio-\\phone.txt", "w", encoding="utf-8") as file:
    for link in job_links:
        if link.strip():  # hooson bish bol
            phone = scrape_phone_number(link.strip())
            file.write(phone + "\n")
        else:  # hooson bol hooson orhin
            file.write("\n")

print("done")
