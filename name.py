import requests
from bs4 import BeautifulSoup
import threading
import time

with open("C:\\Users\\<USER>\\Desktop\\Script\\orgio-\\asd.txt", "r", encoding="utf-8") as f:
    urls = [line.strip() for line in f.readlines() if line.strip()]

company_names = ["" for _ in urls]

MAX_RETRIES = 3
TIMEOUT = 20
THREAD_LIMIT = 5

def fetch_name(index, url, session):
    for attempt in range(MAX_RETRIES):
        try:
            response = session.get(url, timeout=TIMEOUT)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, "html.parser")
                name_tag = soup.select_one("div.name p")
                company_names[index] = name_tag.text.strip() if name_tag else ""
                print(f"done")
                return
            else:
                print(f"aldaaa ({attempt + 1}/{MAX_RETRIES}): {url} - {response.status_code}")
        except requests.RequestException as e:
            print(f"h aldaaaaa ({attempt + 1}/{MAX_RETRIES}): {url} - {e}")
        
        time.sleep(2)

with requests.Session() as session:
    threads = []
    for i, url in enumerate(urls):
        while threading.active_count() > THREAD_LIMIT:
            time.sleep(1)
        
        thread = threading.Thread(target=fetch_name, args=(i, url, session))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()

with open("C:\\Users\\<USER>\\Desktop\\Script\\orgio-\\name.txt", "w", encoding="utf-8") as file:
    for name in company_names:
        file.write(name + "\n")