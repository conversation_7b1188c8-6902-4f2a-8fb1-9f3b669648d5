# Zangia.mn Company Information Scraper

This Python script extracts comprehensive company information from job advertisements posted on Zangia.mn, including company names, website URLs, contact phone numbers, addresses, Facebook URLs, and job listing URLs.

## Features

- **Comprehensive Data Extraction**: Extracts company name, website, phone, address, Facebook URL, and job listing URL
- **Multi-threaded Processing**: Fast parallel processing with configurable worker threads
- **Excel Export**: Saves data in a well-formatted Excel file with auto-adjusted column widths
- **Error Handling**: Robust error handling with retry mechanisms
- **Progress Tracking**: Real-time progress updates and detailed logging
- **Flexible Configuration**: Customizable timeout, retry count, and worker thread settings

## Installation

1. **Install Python Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Ensure you have the input file**: Make sure `asd.txt` contains the company URLs (one per line)

## Usage

### Method 1: Simple Run (Recommended for beginners)
```bash
python run_scraper.py
```
This will:
- Load URLs from `asd.txt`
- Ask if you want to process all companies or a sample
- Run the scraper with default settings
- Save results to an Excel file with timestamp

### Method 2: Command Line with Options
```bash
python zangia_scraper.py [options]
```

**Available Options:**
- `--input, -i`: Input file with URLs (default: asd.txt)
- `--output, -o`: Output Excel filename (default: auto-generated)
- `--workers, -w`: Number of worker threads (default: 5)
- `--timeout, -t`: Request timeout in seconds (default: 20)
- `--retries, -r`: Maximum retries per URL (default: 3)
- `--sample, -s`: Process only first N URLs for testing

**Examples:**
```bash
# Basic usage
python zangia_scraper.py

# Process only first 10 companies for testing
python zangia_scraper.py --sample 10

# Use custom settings
python zangia_scraper.py --workers 10 --timeout 30 --output my_companies.xlsx

# Process different input file
python zangia_scraper.py --input my_urls.txt --output results.xlsx
```

## Output

The script generates an Excel file with the following columns:

| Column | Description |
|--------|-------------|
| Company Name | Name of the company |
| Website URL | Company's official website (or "No website" if not found) |
| Phone Number | Contact phone number (or "No phone" if not found) |
| Address | Company's physical address |
| Facebook URL | Company's Facebook page URL |
| Job Listing URL | URL of the job posting |
| Source URL | Original Zangia.mn company page URL |
| Scrape Status | Success/Failed status |
| Error Message | Error details if scraping failed |

## Performance

- **Speed**: Processes ~100-200 companies per minute (depending on network and server response)
- **Memory**: Low memory usage with streaming processing
- **Reliability**: Automatic retries and error recovery

## Improvements Over Original Code

1. **Unified Codebase**: Combined all separate scripts into one comprehensive solution
2. **Better Error Handling**: Robust error handling with detailed logging
3. **Performance**: Multi-threaded processing for faster execution
4. **Data Quality**: Better phone number extraction and validation
5. **Output Format**: Professional Excel export with formatting
6. **Monitoring**: Progress tracking and summary statistics
7. **Flexibility**: Command-line options for different use cases

## Troubleshooting

**Common Issues:**

1. **"No URLs found"**: Check that `asd.txt` exists and contains valid URLs
2. **Connection errors**: Check internet connection and try reducing worker count
3. **Timeout errors**: Increase timeout value with `--timeout` option
4. **Memory issues**: Reduce worker count with `--workers` option

**Tips for Better Results:**
- Start with a small sample (`--sample 10`) to test
- Use fewer workers (`--workers 3`) if experiencing connection issues
- Increase timeout (`--timeout 30`) for slow connections

## Log Files

The script creates `zangia_scraper.log` with detailed execution logs for debugging.

## Requirements

- Python 3.7+
- Internet connection
- Required packages (see requirements.txt)

## License

This script is for educational and research purposes. Please respect the website's terms of service and rate limits.
