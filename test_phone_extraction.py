#!/usr/bin/env python3
"""
Test script to debug phone number extraction
"""

import logging
from zangia_scraper import ZangiaScraper

# Enable debug logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_phone_extraction():
    print("🔍 Testing Enhanced Phone Number Extraction")
    print("=" * 60)
    
    # Initialize scraper
    scraper = ZangiaScraper(max_workers=1, timeout=20, max_retries=2)
    
    # Load URLs
    urls = scraper.load_urls_from_file('asd.txt')
    
    # Test with first 3 companies
    test_urls = urls[:3]
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n🏢 Testing Company {i}: {url}")
        print("-" * 40)
        
        company_info = scraper.scrape_company_info(url)
        
        print(f"Company Name: {company_info.company_name}")
        print(f"Job Listing URL: {company_info.job_listing_url}")
        print(f"Phone Number: {company_info.phone_number or 'NOT FOUND'}")
        print(f"Status: {company_info.scrape_status}")
        
        if company_info.error_message:
            print(f"Error: {company_info.error_message}")

if __name__ == "__main__":
    test_phone_extraction()
