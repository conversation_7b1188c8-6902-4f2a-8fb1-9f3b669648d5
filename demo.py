#!/usr/bin/env python3
"""
Demo script showing how to use the Zangia scraper
"""

from zangia_scraper import ZangiaScraper
import time

def demo_scraper():
    print("🚀 Zangia.mn Company Information Scraper Demo")
    print("=" * 60)
    
    # Initialize scraper
    scraper = ZangiaScraper(
        max_workers=3,      # Use fewer workers for demo
        timeout=15,         # Shorter timeout for demo
        max_retries=2       # Fewer retries for demo
    )
    
    # Load URLs
    urls = scraper.load_urls_from_file('asd.txt')
    print(f"📁 Loaded {len(urls)} company URLs")
    
    # Process a small sample for demo
    sample_size = 5
    demo_urls = urls[:sample_size]
    print(f"🔍 Processing sample of {sample_size} companies for demo...")
    
    # Start scraping
    start_time = time.time()
    companies_info = scraper.scrape_all_companies(demo_urls)
    end_time = time.time()
    
    # Export results
    output_file = scraper.export_to_excel(companies_info, "demo_results.xlsx")
    
    # Show results
    print("\n" + "="*60)
    print("📊 DEMO RESULTS")
    print("="*60)
    
    for i, company in enumerate(companies_info, 1):
        print(f"\n{i}. {company.company_name or 'Unknown Company'}")
        print(f"   🌐 Website: {company.website_url or 'Not found'}")
        print(f"   📞 Phone: {company.phone_number or 'Not found'}")
        print(f"   📍 Address: {company.address[:50] + '...' if len(company.address) > 50 else company.address}")
        print(f"   📘 Facebook: {company.facebook_url or 'Not found'}")
        print(f"   ✅ Status: {company.scrape_status}")
    
    # Summary
    scraper.print_summary(companies_info)
    print(f"\n⏱️  Processing time: {end_time - start_time:.2f} seconds")
    print(f"💾 Results saved to: {output_file}")
    
    print("\n" + "="*60)
    print("🎉 Demo completed successfully!")
    print("💡 To process all companies, run: python run_scraper.py")
    print("📖 For more options, see: python zangia_scraper.py --help")

if __name__ == "__main__":
    demo_scraper()
