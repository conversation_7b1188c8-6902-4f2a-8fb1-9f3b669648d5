#!/usr/bin/env python3
"""
Test the improved phone number extraction
"""

from zangia_scraper import ZangiaScraper
import time

def test_improved_extraction():
    print("🚀 Testing Improved Phone Number Extraction")
    print("=" * 60)
    
    # Initialize scraper
    scraper = ZangiaScraper(max_workers=3, timeout=15, max_retries=2)
    
    # Load URLs
    urls = scraper.load_urls_from_file('asd.txt')
    
    # Test with first 5 companies
    test_urls = urls[:5]
    
    print(f"📁 Testing {len(test_urls)} companies...")
    
    # Process companies
    start_time = time.time()
    companies_info = scraper.scrape_all_companies(test_urls)
    end_time = time.time()
    
    # Show results
    print("\n" + "="*60)
    print("📊 EXTRACTION RESULTS")
    print("="*60)
    
    for i, company in enumerate(companies_info, 1):
        print(f"\n{i}. {company.company_name or 'Unknown Company'}")
        print(f"   🌐 Website: {company.website_url or 'Not found'}")
        print(f"   📞 Phone: {company.phone_number or 'NOT FOUND'}")
        print(f"   📍 Address: {company.address[:50] + '...' if len(company.address) > 50 else company.address}")
        print(f"   📘 Facebook: {company.facebook_url or 'Not found'}")
        print(f"   🔗 Job URL: {company.job_listing_url or 'Not found'}")
        print(f"   ✅ Status: {company.scrape_status}")
    
    # Summary
    total = len(companies_info)
    with_phone = sum(1 for c in companies_info if c.phone_number)
    
    print("\n" + "="*60)
    print("📈 PHONE EXTRACTION SUMMARY")
    print("="*60)
    print(f"Total companies: {total}")
    print(f"Companies with phone numbers: {with_phone}")
    print(f"Phone extraction rate: {(with_phone/total)*100:.1f}%")
    print(f"Processing time: {end_time - start_time:.2f} seconds")
    
    # Show which phones were found where
    job_phones = sum(1 for c in companies_info if c.phone_number and c.job_listing_url)
    print(f"\nPhone sources:")
    print(f"  - From job listings: {job_phones}")
    print(f"  - From company pages: {with_phone - job_phones}")

if __name__ == "__main__":
    test_improved_extraction()
