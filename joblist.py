import requests
from bs4 import BeautifulSoup

# asd.txt файл доторх бүх URL-ийг унших
with open("C:\\Users\\<USER>\\Desktop\\Script\\orgio-\\asd.txt", "r", encoding="utf-8") as file:
    urls = [line.strip() for line in file.readlines()]  # Бүх мөрийг жагсаалт болгох

job_links = []  # Олдсон холбоосуудыг хадгалах

for url in urls:
    response = requests.get(url)
    if response.status_code == 200:
        soup = BeautifulSoup(response.text, "html.parser")

        job_list = soup.find(class_="job-list")
        if job_list:
            first_job = job_list.find(class_="job item")
            if first_job:
                job_link = first_job.find("a")
                if job_link and job_link.get("href"):
                    full_link = job_link["href"]
                    job_links.append(full_link)
                    #print(f"Холбоос олдлоо: {full_link}")
                else:
                    print(f"Холбоос олдсонгүй: {url}")
                    job_links.append("")  # Add an empty string if no link is found
            else:
                #print(f"Эхний job item олдсонгүй: {url}")
                job_links.append("")  # Add an empty string if no job item is found
        else:
            print(f"job-list олдсонгүй: {url}")
            job_links.append("")  # Add an empty string if no job-list is found
    else:
        print(f"Вэб хуудсыг уншихад алдаа гарлаа: {url} - Код: {response.status_code}")
        job_links.append("")  # Add an empty string in case of a request error

# Олдсон бүх холбоосыг файлд хадгалах
with open("C:\\Users\\<USER>\\Desktop\\Script\\orgio-\\joblist.txt", "w", encoding="utf-8") as file:
    file.write("\n".join(job_links))  # Join the links with new lines
print("Бүх холбоос C:\\Users\\<USER>\\Desktop\\Script\\orgio-\\joblist.txt файлд хадгалагдлаа.")
