import requests
from bs4 import BeautifulSoup
import threading
import time

# Файлнаас URL жагсаалт унших
with open("C:\\Users\\<USER>\\Desktop\\Script\\orgio-\\asd.txt", "r", encoding="utf-8") as f:
    urls = [line.strip() for line in f.readlines() if line.strip()]

# Хаяг хадгалах list
company_data = ["" for _ in urls]

# Retry тохиргоо
MAX_RETRIES = 3
TIMEOUT = 20
THREAD_LIMIT = 5  # Нэг дор ажиллах thread-ийн тоог хязгаарлах

def fetch_info(index, url, session):
    for attempt in range(MAX_RETRIES):
        try:
            response = session.get(url, timeout=TIMEOUT)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, "html.parser")
                address_tag = soup.select_one("div.org-address")
                
                address = address_tag.text.strip() if address_tag else ""
                
                company_data[index] = address
                #print(f"Хадгалсан: {company_data[index] if company_data[index] else 'Хоосон мөр'}")
                return
            else:
                print(f"Алдаа гарлаа ({attempt + 1}/{MAX_RETRIES}): {url} - {response.status_code}")
        except requests.RequestException as e:
            print(f"Холболтын алдаа ({attempt + 1}/{MAX_RETRIES}): {url} - {e}")
        
        time.sleep(2)  # Дахин оролдохын өмнө бага зэрэг хүлээх

# Олон хүсэлтийг зэрэг илгээх
with requests.Session() as session:
    threads = []
    for i, url in enumerate(urls):
        while threading.active_count() > THREAD_LIMIT:
            time.sleep(1)  # Thread хэт ачааллаас сэргийлэх
        
        thread = threading.Thread(target=fetch_info, args=(i, url, session))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()

# Файлд хадгалах
with open("C:\\Users\\<USER>\\Desktop\\Script\\orgio-\\hayg.txt", "w", encoding="utf-8") as file:
    for data in company_data:
        file.write(data + "\n")