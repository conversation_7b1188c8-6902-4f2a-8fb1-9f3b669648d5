#!/usr/bin/env python3
"""
Debug script to manually test phone extraction on a specific job page
"""

import requests
from bs4 import BeautifulSoup
import re
from zangia_scraper import <PERSON>ang<PERSON><PERSON><PERSON><PERSON><PERSON>

def test_specific_job_page():
    # Test with a known job URL
    base_url = "https://www.zangia.mn/"
    job_path = "job/_9h8ycuz2vb"  # From the test above
    full_job_url = base_url + job_path
    
    print(f"🔍 Testing job page: {full_job_url}")
    
    scraper = ZangiaScraper()
    
    try:
        # Test the job page directly
        response = requests.get(full_job_url, timeout=20)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Get page text
            page_text = soup.get_text()
            print(f"Page text length: {len(page_text)}")
            
            # Look for phone patterns manually
            phone_patterns = [
                r'\d{8}',
                r'\d{2}[-\s]\d{6}',
                r'(?:\+976|976)[-\s]?\d{8}',
                r'утас[:\s]*\d{8}',
                r'холбоо[:\s]*\d{8}',
                r'тел[:\s]*\d{8}'
            ]
            
            print("\n📞 Looking for phone patterns:")
            for pattern in phone_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                if matches:
                    print(f"Pattern '{pattern}': {matches}")
            
            # Test our extraction method
            phone = scraper.extract_phone_number(page_text)
            print(f"\n🎯 Our extraction result: '{phone}'")
            
            # Test job page extraction method
            job_phone = scraper.scrape_phone_from_job_page(full_job_url)
            print(f"🎯 Job page extraction result: '{job_phone}'")
            
            # Show some sample text
            print(f"\n📄 Sample page content (first 500 chars):")
            print(page_text[:500])
            
        else:
            print(f"❌ Failed to fetch page: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_company_page():
    # Test company page
    company_url = "https://www.zangia.mn/company/Nutgiin-buyan-groupLLC"
    print(f"\n🏢 Testing company page: {company_url}")
    
    scraper = ZangiaScraper()
    
    try:
        response = requests.get(company_url, timeout=20)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            page_text = soup.get_text()
            
            # Test our extraction method
            phone = scraper.extract_phone_number(page_text)
            print(f"🎯 Company page extraction result: '{phone}'")
            
            # Test company page extraction method
            company_phone = scraper.scrape_phone_from_company_page(soup)
            print(f"🎯 Company page method result: '{company_phone}'")
            
            # Show some sample text
            print(f"\n📄 Sample company content (first 500 chars):")
            print(page_text[:500])
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_specific_job_page()
    test_company_page()
